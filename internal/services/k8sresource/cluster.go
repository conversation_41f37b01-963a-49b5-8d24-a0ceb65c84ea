package k8sresource

import (
	"context"
	"fmt"

	"github.com/lib/pq"
	"github.com/samber/lo"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	agentv1 "github.com/akuityio/akuity-platform/agent/pkg/api/gen/agent/v1"
	"github.com/akuityio/akuity-platform/models/models"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"

	_ "embed"
)

//go:embed cluster.sql
var clusterSQL string

type EnabledCluster struct {
	ID                   string    `boil:"id"`
	Name                 string    `boil:"name"`
	Shard                string    `boil:"shard"`
	InstanceID           string    `boil:"instance_id"`
	InstanceName         string    `boil:"instance_name"`
	K8sInfo              null.JSON `boil:"k8s_info"`
	CustomDeprecatedAPIs null.JSON `boil:"custom_deprecated_apis"`
	AgentVersion         string    `boil:"agent_version"`
	IsDegraded           bool      `boil:"is_degraded"`
	IsEnabled            bool      `boil:"is_enabled"`
	InstanceHostname     string    `boil:"instance_hostname"`
	LastConnectTime      string    `boil:"last_connect_time"`
}

func (i *EnabledCluster) GetK8sInfo() (*agentv1.ClusterKubernetesInfo, error) {
	var info *agentv1.ClusterKubernetesInfo
	if err := i.K8sInfo.Unmarshal(&info); err != nil {
		return nil, err
	}
	return info, nil
}

func (i *EnabledCluster) GetCustomDeprecatedAPIs() ([]*models.CustomDeprecatedAPI, error) {
	var customDeprecatedAPIs []*models.CustomDeprecatedAPI
	if err := i.CustomDeprecatedAPIs.Unmarshal(&customDeprecatedAPIs); err != nil {
		return nil, err
	}
	return customDeprecatedAPIs, nil
}

type EnabledClustersInfo struct {
	Clusters map[string]*EnabledCluster
}

func (i *EnabledClustersInfo) GetClusterIDs() []string {
	return lo.MapToSlice(i.Clusters, func(clusterID string, _ *EnabledCluster) string {
		return clusterID
	})
}

func (i *EnabledClustersInfo) GetEnabledClusterIDs() []string {
	ids := make([]string, 0, len(i.Clusters))
	for _, cluster := range i.Clusters {
		if cluster.IsEnabled {
			ids = append(ids, cluster.ID)
		}
	}
	return ids
}

func (i *EnabledClustersInfo) GetClusters() []*EnabledCluster {
	return lo.Values(i.Clusters)
}

func (i *EnabledClustersInfo) GetEnabledClusters() []*EnabledCluster {
	return lo.Filter(lo.Values(i.Clusters), func(cluster *EnabledCluster, _ int) bool {
		return cluster.IsEnabled
	})
}

func (i *EnabledClustersInfo) GetCluster(clusterID string) *EnabledCluster {
	return i.Clusters[clusterID]
}

func (i *EnabledClustersInfo) GetClusterByName(instanceID, clusterName string) *EnabledCluster {
	for _, cluster := range i.Clusters {
		if cluster.InstanceID == instanceID && cluster.Name == clusterName {
			return cluster
		}
	}
	return nil
}

func (i *EnabledClustersInfo) GetEnabledInstanceIDs() []string {
	instanceIDs := make(map[string]struct{})
	for _, cluster := range i.Clusters {
		if cluster.IsEnabled {
			instanceIDs[cluster.InstanceID] = struct{}{}
		}
	}
	return lo.Keys(instanceIDs)
}

func (i *EnabledClustersInfo) GetKubernetesVersions() map[string]string {
	return lo.MapValues(i.Clusters, func(cluster *EnabledCluster, _ string) string {
		info, err := cluster.GetK8sInfo()
		if err != nil {
			return ""
		}
		return info.KubernetesVersion
	})
}

func (s *Service) GetEnabledClustersInfo(ctx context.Context, instanceID string, includeDisabled bool, clusterIDs ...string) (*EnabledClustersInfo, error) {
	clusterIDs = lo.Filter(clusterIDs, func(c string, _ int) bool { return c != "" })
	queryParams := []interface{}{
		s.organizationID,
		instanceID,
		pq.Array(clusterIDs),
	}
	var enabledClusters []*EnabledCluster
	if err := models.NewQuery(qm.SQL(clusterSQL,
		queryParams...,
	)).Bind(ctx, s.db, &enabledClusters); err != nil {
		return nil, fmt.Errorf("failed to fetch enabled clusters: %w", err)
	}
	if !includeDisabled {
		enabledClusters = lo.Filter(enabledClusters, func(c *EnabledCluster, _ int) bool {
			return c.IsEnabled
		})
	}
	return &EnabledClustersInfo{
		Clusters: lo.SliceToMap(enabledClusters, func(c *EnabledCluster) (string, *EnabledCluster) {
			return c.ID, c
		}),
	}, nil
}

func (s *Service) ValidateGetRequest(instanceID, clusterID string) (*EnabledCluster, error) {
	if instanceID == "" {
		return nil, status.Error(codes.InvalidArgument, "instance ID must be provided")
	}
	if clusterID == "" {
		return nil, status.Error(codes.InvalidArgument, "cluster ID must be provided")
	}
	clusterInfo, err := s.GetEnabledClustersInfo(context.Background(), instanceID, false, clusterID)
	if err != nil {
		return nil, err
	}
	cluster := clusterInfo.GetCluster(clusterID)
	if cluster == nil {
		return nil, status.Error(codes.InvalidArgument, "Akuity Intelligence is not enabled for the provided cluster id")
	}
	return cluster, nil
}

//go:embed cluster_detail.sql
var clusterDetailSQL string

func (s *Service) GetKubernetesClusterDetail(ctx context.Context, cluster *EnabledCluster) (*organizationv1.GetKubernetesClusterDetailResponse, error) {
	var result []models.ArgoCDClusterK8SObject
	if err := models.NewQuery(qm.SQL(clusterDetailSQL, s.organizationID, cluster.InstanceID, cluster.ID)).Bind(ctx, s.db, &result); err != nil {
		return nil, fmt.Errorf("failed to fetch cluster detail: %w", err)
	}

	detail := &organizationv1.GetKubernetesClusterDetailResponse{
		Id:            cluster.ID,
		Name:          cluster.Name,
		CpuRequest:    0,
		MemoryRequest: 0,
	}

	var cpuUsage, memoryUsage *float64
	podTotal := 0
	for _, object := range result {
		switch object.Kind.String {
		case "Node":
			columns := NodeColumns{}
			if err := object.Columns.Unmarshal(&columns); err != nil {
				return nil, err
			}
			detail.CpuAllocatable += columns.AllocatableCPU
			detail.MemoryAllocatable += columns.AllocatableMemory
			if columns.UsageCPU != nil {
				if cpuUsage == nil {
					cpuUsage = new(float64)
				}
				*cpuUsage += *columns.UsageCPU
			}
			if columns.UsageMemory != nil {
				if memoryUsage == nil {
					memoryUsage = new(float64)
				}
				*memoryUsage += *columns.UsageMemory
			}
			detail.PodAllocatable += uint32(columns.AllocatablePod)
			detail.NodeCount++
		case "Pod":
			columns := PodColumns{}
			if err := object.Columns.Unmarshal(&columns); err != nil {
				return nil, err
			}
			if columns.Phase == "Running" {
				detail.PodRunning++
			}
			podTotal++
		case "Container":
			columns := ContainerColumns{}
			if err := object.Columns.Unmarshal(&columns); err != nil {
				return nil, err
			}
			if columns.State != "running" {
				continue
			}
			if columns.RequestsCPU != nil {
				detail.CpuRequest += *columns.RequestsCPU
			}
			if columns.RequestsMemory != nil {
				detail.MemoryRequest += *columns.RequestsMemory
			}
		}
	}
	detail.PodTotal = uint32(podTotal)
	detail.CpuUsage = cpuUsage
	detail.MemoryUsage = memoryUsage
	return detail, nil
}
