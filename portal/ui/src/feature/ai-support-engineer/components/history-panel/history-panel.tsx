import {
  faClose,
  faSearch,
  faEdit,
  faTrash,
  faEye,
  faEyeSlash,
  faRefresh,
  faFire,
  faComment,
  faCheck,
  faChevronCircleLeft,
  faChevronCircleRight,
  faFilter
} from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Button, Input, Flex, Skeleton, Modal, ConfigProvider, Tooltip, Tag } from 'antd';
import classNames from 'classnames';
import { useState, useCallback } from 'react';

import {
  useUpdateAIConversationMutation,
  useDeleteAIConversationMutation,
  useUpdateIncidentMutation
} from '@ui/lib/apiclient/organization/ai-queries';
import {
  AIConversation,
  AIMessage,
  UpdateAIConversationRequest
} from '@ui/lib/apiclient/organization/v1/organization_pb';
import { useAISupportEngineerContext } from '@ui/lib/context/ai-support-engineer-context';
import { useNotificationContext } from '@ui/lib/context/notification-context';
import { useNativeSearchParams } from '@ui/lib/hooks/use-native-search-params';

import { timestampToDate } from '../../utils';

import { FilterModal, HistoryPanelFilter } from './filter-modal';

interface HistoryPanelProps {
  onClose: () => void;
  clearSelection: () => void;
  onSelectChat: (messages: AIMessage[], conversationId: string) => void;
  conversations: AIConversation[];
  conversationsTotalCount: number;
  drawerWidth: number;
  isLoading?: boolean;
  onConversationListUpdate?: () => void;
  onConversationUpdate?: (conversationId: string) => void;
  onConversationDelete?: (conversationId: string) => void;
  filter: HistoryPanelFilter;
  onFilterChange: (filter: HistoryPanelFilter) => void;
  selectedConversationId: string;
}

export const HistoryPanel = ({
  onClose,
  clearSelection,
  onSelectChat,
  conversations,
  conversationsTotalCount,
  drawerWidth,
  isLoading = false,
  onConversationListUpdate,
  onConversationUpdate,
  onConversationDelete,
  filter,
  onFilterChange,
  selectedConversationId
}: HistoryPanelProps) => {
  const notification = useNotificationContext();
  const [editingChat, setEditingChat] = useState<AIConversation | null>(null);
  const [editTitle, setEditTitle] = useState('');
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);
  const [isDeleteModalVisible, setIsDeleteModalVisible] = useState(false);
  const [deletingChat, setDeletingChat] = useState<AIConversation | null>(null);
  const [isFilterModalVisible, setIsFilterModalVisible] = useState(false);

  const [modal, contextHolder] = Modal.useModal();
  const updateConversationMutation = useUpdateAIConversationMutation();
  const deleteConversationMutation = useDeleteAIConversationMutation();
  const { mutateAsync: updateIncident } = useUpdateIncidentMutation();
  const { isInKargo } = useAISupportEngineerContext();

  const path = isInKargo ? '/ext/intelligence' : '/kubevision';

  const [, setNativeSearchParams] = useNativeSearchParams(path);

  const handleRunbookLink = useCallback(
    (e: React.MouseEvent, instanceId: string, runbook: string) => {
      e.stopPropagation();

      if (!instanceId || !runbook) {
        return;
      }

      const searchParams = new URLSearchParams();
      searchParams.set('tab', 'kubevision');
      searchParams.set('dashboard', 'Incidents');
      searchParams.set('incidentTab', 'runbooks');
      searchParams.set('instanceId', instanceId);
      searchParams.set('runbook', runbook);
      searchParams.delete('akuity-chat');

      setNativeSearchParams(searchParams);
    },
    [isInKargo, setNativeSearchParams]
  );

  const isFilterOn = filter.incidentOnly;

  const handleToggleIncident = useCallback(
    (e: React.MouseEvent, chat: AIConversation) => {
      const chatTypeName = chat.incident ? 'incident' : 'conversation';
      modal.confirm({
        title: `Are you sure you want to convert ${chatTypeName} to ${chatTypeName === 'incident' ? 'conversation' : 'incident'}?`,
        content: `This will convert the ${chatTypeName} to the ${chatTypeName === 'incident' ? 'conversation' : 'incident'}.`,
        onOk: async () => {
          e.stopPropagation();
          try {
            await updateConversationMutation.mutateAsync({
              id: chat.id,
              incident: chat.incident ? null : { resolved: false },
              title: chat.title,
              public: chat.public,
              contexts: chat.contexts
            });
            onConversationListUpdate?.();
            onConversationUpdate?.(chat.id);
          } catch (error) {
            notification.error({
              message: `Failed to update ${chatTypeName}`,
              placement: 'bottomRight'
            });
          }
        }
      });
    },
    [updateConversationMutation, onConversationListUpdate, onConversationUpdate]
  );

  const handleChatSelect = useCallback(
    (chat: AIConversation) => {
      onSelectChat(chat.messages, chat.id);
    },
    [onSelectChat]
  );

  const handleEditClick = useCallback((e: React.MouseEvent, chat: AIConversation) => {
    e.stopPropagation();
    setEditingChat(chat);
    setEditTitle(chat.title || 'New Conversation');
    setIsEditModalVisible(true);
  }, []);

  const handleTogglePublic = useCallback(
    async (e: React.MouseEvent, chat: AIConversation) => {
      const chatTypeName = chat.incident ? 'Incident' : 'Conversation';
      e.stopPropagation();
      modal.confirm({
        title: `Are you sure you want to ${chat.public ? 'unpublish' : 'publish'} this ${chatTypeName.toLowerCase()}?`,
        content: chat.public
          ? `This will make the ${chatTypeName.toLowerCase()} private and remove it from public view.`
          : `This will make the ${chatTypeName.toLowerCase()} public and visible to others.`,
        onOk: async () => {
          try {
            await updateConversationMutation.mutateAsync({
              id: chat.id,
              title: chat.title,
              public: !chat.public,
              incident: chat.incident ? { resolved: !!chat.incident.resolvedAt } : null,
              contexts: chat.contexts
            });
            notification.success({
              message: `${chatTypeName} ${chat.public ? 'unpublished' : 'published'} successfully`,
              placement: 'bottomRight'
            });
            onConversationListUpdate?.();
            onConversationUpdate?.(chat.id);
          } catch (error) {
            notification.error({
              message: `Failed to update ${chatTypeName.toLowerCase()} visibility`,
              placement: 'bottomRight'
            });
          }
        }
      });
    },
    [updateConversationMutation, onConversationListUpdate, onConversationUpdate]
  );

  const handleDeleteClick = useCallback((e: React.MouseEvent, chat: AIConversation) => {
    e.stopPropagation();
    setDeletingChat(chat);
    setIsDeleteModalVisible(true);
  }, []);

  const handleEditSubmit = useCallback(async () => {
    if (!editingChat) return;
    const chatTypeName = editingChat.incident ? 'Incident' : 'Conversation';

    try {
      await updateConversationMutation.mutateAsync({
        id: editingChat.id,
        title: editTitle,
        public: editingChat.public,
        incident: editingChat.incident ? { resolved: !!editingChat.incident.resolvedAt } : null,
        contexts: editingChat.contexts
      });
      notification.success({
        message: `${chatTypeName} title updated successfully`,
        placement: 'bottomRight'
      });
      setIsEditModalVisible(false);
      onConversationListUpdate?.();
      onConversationUpdate?.(editingChat.id);
    } catch (error) {
      notification.error({
        message: `Failed to update ${chatTypeName.toLowerCase()} title`,
        placement: 'bottomRight'
      });
    }
  }, [
    editingChat,
    editTitle,
    updateConversationMutation,
    onConversationListUpdate,
    onConversationUpdate
  ]);

  const handleDeleteConfirm = useCallback(async () => {
    if (!deletingChat) return;
    const chatTypeName = deletingChat.incident ? 'Incident' : 'Conversation';

    try {
      await deleteConversationMutation.mutateAsync({
        id: deletingChat.id
      });
      notification.success({
        message: `${chatTypeName} deleted successfully`,
        placement: 'bottomRight'
      });
      if (deletingChat.id === selectedConversationId) {
        clearSelection();
      }
      setIsDeleteModalVisible(false);
      onConversationListUpdate?.();
      onConversationDelete?.(deletingChat.id);
    } catch (error) {
      notification.error({
        message: `Failed to delete ${chatTypeName.toLowerCase()}`,
        placement: 'bottomRight'
      });
    }
  }, [deletingChat, deleteConversationMutation, onConversationListUpdate, onConversationDelete]);

  const renderLoadingState = () => (
    <div className='px-4'>
      {[1, 2, 3, 4, 5].map((key) => (
        <div key={key} className='py-4 border-b border-[#f0f0f0]'>
          <Skeleton
            active
            paragraph={{ rows: 1, width: ['60%'] }}
            title={{ width: '80%' }}
            className='opacity-60'
          />
        </div>
      ))}
    </div>
  );

  return (
    <ConfigProvider
      theme={{
        components: {
          Modal: {
            zIndexPopupBase: 1002
          }
        }
      }}
    >
      <Flex
        vertical
        className='absolute top-[25px] w-[320px] h-[calc(100vh-50px)] border-r border-[#f0f0f0] rounded-2xl shadow-2xl overflow-hidden bg-white z-[1001]'
        style={{ right: `${drawerWidth + 25}px` }}
      >
        <Flex vertical gap={2}>
          <Flex
            justify='space-between'
            align='center'
            className='sticky top-0 z-[1] px-4 pt-4 pb-1 border-b border-[#f0f0f0]'
          >
            <h3 className='m-0'>Conversation History</h3>
            <Button type='text' icon={<FontAwesomeIcon icon={faClose} />} onClick={onClose} />
          </Flex>
          <Flex className='px-4 py-1 border-b border-[#f0f0f0]' gap={2}>
            <Input
              prefix={<FontAwesomeIcon icon={faSearch} className='text-gray-400' />}
              placeholder='Search conversations...'
              value={filter.titleContains}
              onChange={(e) => {
                onFilterChange({ ...filter, titleContains: e.target.value });
              }}
              className='w-full'
              allowClear
            />
            <Button
              type='text'
              icon={
                <FontAwesomeIcon icon={faRefresh} className='text-gray-400 hover:text-gray-600' />
              }
              onClick={() => onConversationListUpdate()}
            />
          </Flex>
          <Flex className='justify-between items-center px-4 py-1' gap={2}>
            <div>
              {!isLoading && (
                <div className='result-text'>
                  {conversationsTotalCount} conversation{conversationsTotalCount > 1 ? 's' : ''}
                </div>
              )}
            </div>
            <div>
              <Button
                type={isFilterOn ? 'primary' : 'text'}
                icon={<FontAwesomeIcon icon={faFilter} />}
                onClick={() => {
                  setIsFilterModalVisible(true);
                }}
                className='px-1'
                size='small'
              >
                Filters
              </Button>
              <Button
                type='text'
                icon={<FontAwesomeIcon icon={faChevronCircleLeft} />}
                onClick={() => {
                  let newOffset = filter.offset - filter.limit;
                  if (newOffset < 0) {
                    newOffset = 0;
                  }
                  onFilterChange({ ...filter, offset: newOffset });
                }}
                disabled={filter.offset === 0}
              />
              <Button
                type='text'
                icon={<FontAwesomeIcon icon={faChevronCircleRight} />}
                onClick={() => {
                  let newOffset = filter.offset + filter.limit;
                  if (newOffset > conversationsTotalCount) {
                    newOffset = conversationsTotalCount;
                  }
                  onFilterChange({ ...filter, offset: newOffset });
                }}
                disabled={filter.offset + filter.limit >= conversationsTotalCount}
              />
            </div>
          </Flex>
        </Flex>

        <div className='overflow-y-auto flex-grow'>
          {isLoading ? (
            renderLoadingState()
          ) : (
            <>
              {conversations.map((chat) => (
                <div
                  key={chat.id}
                  className={classNames('history-item group', {
                    'history-item-selected': selectedConversationId === chat.id
                  })}
                  onClick={() => handleChatSelect(chat)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      e.preventDefault();
                      handleChatSelect(chat);
                    }
                  }}
                  role='button'
                  tabIndex={0}
                >
                  <div className='flex flex-col'>
                    <div className='title line-clamp-2'>
                      <Tooltip
                        title={chat.incident ? 'Convert to conversation' : 'Convert to incident'}
                      >
                        <FontAwesomeIcon
                          className={classNames('hover:text-gray-600 mr-2', {
                            'text-red-500': chat.incident && !chat.incident.resolvedAt,
                            'text-green-500': chat.incident && chat.incident.resolvedAt,
                            'text-gray-400': !chat.incident
                          })}
                          icon={
                            (chat.incident && chat.incident.resolvedAt && faCheck) ||
                            (chat.incident && faFire) ||
                            faComment
                          }
                          onClick={(e) => handleToggleIncident(e, chat)}
                        />
                      </Tooltip>{' '}
                      <Tooltip title={chat.title || 'New Conversation'}>
                        {chat.title || 'New Conversation'}
                      </Tooltip>
                    </div>

                    <div className='flex justify-between items-center'>
                      <div className='timestamp'>
                        {timestampToDate(chat.lastUpdateTime).toLocaleString()}
                      </div>
                      {(chat.ownedByMe && (
                        <div className='action-buttons flex gap-2'>
                          <Button
                            type='text'
                            icon={
                              <FontAwesomeIcon
                                icon={chat.public ? faEye : faEyeSlash}
                                className='text-gray-400 hover:text-gray-600'
                              />
                            }
                            onClick={(e) => handleTogglePublic(e, chat)}
                            className='p-1'
                          />
                          <Button
                            type='text'
                            icon={
                              <FontAwesomeIcon
                                icon={faEdit}
                                className='text-gray-400 hover:text-gray-600'
                              />
                            }
                            onClick={(e) => handleEditClick(e, chat)}
                            className='p-1'
                          />
                          <Button
                            type='text'
                            icon={
                              <FontAwesomeIcon
                                icon={faTrash}
                                className='text-gray-400 hover:text-red-500'
                              />
                            }
                            onClick={(e) => handleDeleteClick(e, chat)}
                            className='p-1'
                          />
                        </div>
                      )) || (
                        <FontAwesomeIcon
                          icon={chat.public ? faEye : faEyeSlash}
                          className='text-gray-400 hover:text-gray-600'
                          title='Public Conversation'
                        />
                      )}
                    </div>
                    {chat.incident && selectedConversationId == chat.id && (
                      <>
                        {[
                          { field: chat.incident.summary, title: 'Summary' },
                          { field: chat.incident.rootCause, title: 'Root Cause' },
                          { field: chat.incident.resolution, title: 'Resolution' }
                        ]
                          .filter(({ field }) => field)
                          .map(({ field, title }) => (
                            <div key={field} className='text-sm text-gray-500 mt-2'>
                              <strong>{title}:</strong>{' '}
                              <Tooltip title={field}>
                                <span className='line-clamp-3'>{field}</span>
                              </Tooltip>
                            </div>
                          ))}
                        {(chat.incident.runbooks || []).length > 0 && (
                          <div className='flex flex-wrap gap-2 mt-2 text-sm text-gray-500'>
                            <strong>Runbooks:</strong>{' '}
                            {chat.incident.runbooks.map((item) => (
                              <Tag
                                key={item}
                                onClick={(e) =>
                                  handleRunbookLink(e, chat.incident.instanceId, item)
                                }
                              >
                                {item}
                              </Tag>
                            ))}
                          </div>
                        )}
                      </>
                    )}
                  </div>
                  <div className='mt-2'>
                    {selectedConversationId === chat.id && chat.incident && (
                      <Button
                        type='primary'
                        onClick={() => {
                          const isResolved = !!chat.incident.resolvedAt;
                          modal.confirm({
                            title: isResolved ? 'Mark as Unresolved' : 'Mark as Resolved',
                            content: `Are you sure you want to mark this incident as ${isResolved ? 'unresolved' : 'resolved'}?`,
                            onOk: async () => {
                              await updateIncident(
                                UpdateAIConversationRequest.fromJson({
                                  id: chat?.id,
                                  instanceId: chat?.instanceId,
                                  title: chat?.title,
                                  public: chat?.public,
                                  contexts: chat?.contexts.map((context) => context.toJson()),
                                  incident: {
                                    resolved: !isResolved
                                  }
                                })
                              );
                              onConversationListUpdate?.();
                              onConversationUpdate?.(chat.id);
                            }
                          });
                        }}
                      >
                        {chat.incident.resolvedAt ? 'Mark as Unresolved' : 'Mark as Resolved'}
                      </Button>
                    )}
                  </div>
                </div>
              ))}
              {filter.titleContains && conversations.length === 0 && (
                <div className='no-results'>
                  No conversations found matching "{filter.titleContains}"
                </div>
              )}
            </>
          )}
        </div>
      </Flex>

      <Modal
        title={`Edit ${editingChat?.incident ? 'Incident' : 'Conversation'} Title`}
        open={isEditModalVisible}
        onOk={handleEditSubmit}
        onCancel={() => setIsEditModalVisible(false)}
        confirmLoading={updateConversationMutation.isPending}
      >
        <Input
          value={editTitle}
          onChange={(e) => setEditTitle(e.target.value)}
          placeholder='Enter new title'
        />
      </Modal>

      <Modal
        title={`Delete ${deletingChat?.incident ? 'Incident' : 'Conversation'}`}
        open={isDeleteModalVisible}
        onOk={handleDeleteConfirm}
        onCancel={() => setIsDeleteModalVisible(false)}
        confirmLoading={deleteConversationMutation.isPending}
      >
        <p>
          Are you sure you want to delete this{' '}
          {deletingChat?.incident ? 'incident' : 'conversation'}? This action cannot be undone.
        </p>
      </Modal>

      <FilterModal
        isFilterModalVisible={isFilterModalVisible}
        setIsFilterModalVisible={setIsFilterModalVisible}
        initialFilter={filter}
        onFilterChange={(newFilter) => {
          onFilterChange({ ...filter, ...newFilter, offset: 0 });
        }}
      />
      {contextHolder}
    </ConfigProvider>
  );
};
