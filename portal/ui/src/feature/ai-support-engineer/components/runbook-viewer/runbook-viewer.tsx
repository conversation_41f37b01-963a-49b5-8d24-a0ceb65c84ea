import { faWandMagicSparkles } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Button, Modal } from 'antd';
import { useState } from 'react';

import { useModal } from '@ui/lib/hooks';
import { renderMarkdown } from '@ui/lib/utils';

import './runbook-viewer.less';

interface RunbookData {
  content: string;
  stored?: boolean;
  title: string;
}

interface RunbookViewerProps {
  runbook: RunbookData;
  messageTypeClass: string;
  onSendMessage: (message: string) => void;
  storePrompt?: string;
}

export const RunbookViewer = ({
  runbook,
  messageTypeClass,
  onSendMessage,
  storePrompt
}: RunbookViewerProps) => {
  const { show: showConfirmModal, hide: hideConfirmModal } = useModal();
  const [isStoring, setIsStoring] = useState(false);

  const handleStoreRunbook = () => {
    showConfirmModal(({ visible, hide }) => (
      <Modal
        title={runbook.title}
        open={visible}
        onOk={confirmStoreRunbook}
        onCancel={hide}
        okText='Store'
        cancelText='Cancel'
      >
        <p>Store this runbook for future incidents?</p>
      </Modal>
    ));
  };

  const confirmStoreRunbook = () => {
    setIsStoring(true);
    const message =
      storePrompt || `Please store the runbook from the previous message.`;
    onSendMessage(message);
    hideConfirmModal();
  };

  return (
    <div className={`runbook-container ${messageTypeClass}`}>
      <div className='runbook-content-wrapper'>
        <div
          className='runbook-markdown text-sm markdown-content'
          dangerouslySetInnerHTML={{ __html: renderMarkdown(runbook.content) }}
        />
        {messageTypeClass === 'bot-message' && storePrompt && (
          <div className='runbook-footer'>
            <Button
              onClick={handleStoreRunbook}
              size='small'
              type='primary'
              icon={<FontAwesomeIcon icon={faWandMagicSparkles} />}
              disabled={isStoring}
            >
              {runbook.title}
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};
