import { PlainMessage, Timestamp } from '@bufbuild/protobuf';
import { faThumbsUp, faThumbsDown } from '@fortawesome/free-regular-svg-icons';
import { faMinusSquare, faPlusSquare, faSpinner } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Collapse, Button, Steps } from 'antd';
import { useState, forwardRef } from 'react';

import { formatTime } from '@ui/feature/kubevision/utils';
import { useUpdateAIMessageFeedbackMutation } from '@ui/lib/apiclient/organization/ai-queries';
import {
  AIConversationStepStatus,
  AIMessage,
  AIMessageContext
} from '@ui/lib/apiclient/organization/v1/organization_pb';
import { renderMarkdown } from '@ui/lib/utils';

import { timestampToDate } from '../../utils';
import { DiffViewer } from '../diff-viewer/diff-viewer';
import { RunbookViewer } from '../runbook-viewer/runbook-viewer';
import { SuggestedContexts } from '../suggested-contexts/suggested-contexts';

import './message-bubble.less';

interface MessageBubbleProps {
  message?: PlainMessage<AIMessage>;
  representativeTime?: Timestamp;
  conversationId?: string;
  onSendMessage: (message: string) => void;
  contexts: PlainMessage<AIMessageContext>[];
  updateContexts: (contexts: PlainMessage<AIMessageContext>[]) => void;
}

export const MessageBubble = forwardRef<HTMLDivElement, MessageBubbleProps>(
  (
    { message, representativeTime, onSendMessage, conversationId, contexts, updateContexts },
    ref
  ) => {
    const isUserMessage = message.role === 'user';
    const messageTypeClass = message.ownedByMe ? 'user-message' : 'bot-message';

    const [thumbsUpActive, setThumbsUpActive] = useState(false);
    const [thumbsDownActive, setThumbsDownActive] = useState(false);

    const { mutate: updateFeedback } = useUpdateAIMessageFeedbackMutation();

    const handleFeedback = (isUseful: boolean) => {
      if ((isUseful && thumbsUpActive) || (!isUseful && thumbsDownActive)) {
        return;
      }
      setThumbsUpActive(isUseful);
      setThumbsDownActive(!isUseful);
      updateFeedback({
        conversationId,
        messageId: message.id,
        isUseful
      });
    };

    const finalTimestamp = isUserMessage ? message?.createTime : representativeTime;

    const validChanges = message.suggestedChanges.filter(
      (change) => change.patch && change.patch.length > 0 && change.patch !== '{}'
    );

    const groups = [
      { changes: validChanges.filter((c) => !c.applied), title: 'Suggested Changes' },
      { changes: validChanges.filter((c) => c.applied), title: 'Applied Changes' }
    ];

    return (
      <div
        className={`message-container mb-4 ${
          messageTypeClass === 'user-message' ? 'pl-16' : ''
        } animate-fade-in`}
      >
        <div className='message-wrapper'>
          <div
            className={`p-3 rounded-lg message-bubble ${
              messageTypeClass === 'bot-message'
                ? 'bot-message gradient-bg-blue'
                : 'user-message gradient-bg-gray'
            }`}
          >
            {!message.ownedByMe && (
              <div className='text-xs text-gray-400 mb-1'>
                <strong>{message.username || message.role}</strong>
              </div>
            )}
            {message.thinkingProcess && (
              <Collapse
                className='thinking-process-collapse'
                ghost
                expandIcon={(props) => (
                  <Button
                    icon={<FontAwesomeIcon icon={props.isActive ? faMinusSquare : faPlusSquare} />}
                    type={'default'}
                    size='small'
                  >
                    {props.isActive ? 'Collapse' : 'Expand'}
                  </Button>
                )}
                expandIconPosition='end'
                items={[
                  {
                    key: '1',
                    label: (
                      <strong className='text-sm thinking-process-title'>Thinking Process:</strong>
                    ),
                    children: (
                      <div className='thinking-process-container'>
                        <div
                          className='thinking-process-content text-sm rounded'
                          dangerouslySetInnerHTML={{
                            __html: renderMarkdown(message.thinkingProcess)
                          }}
                        />
                      </div>
                    )
                  }
                ]}
              />
            )}

            {message.content && (
              <div
                className='text-sm markdown-content mt-2'
                dangerouslySetInnerHTML={{ __html: renderMarkdown(message.content) }}
              />
            )}
            {message.suggestedChanges?.length > 0 && (
              <>
                {groups.map(
                  ({ changes, title }) =>
                    changes.length > 0 && (
                      <div key={title}>
                        <strong className='block mt-4 mb-2 text-sm'>{title}:</strong>
                        {changes.map((suggestedChange, index) => (
                          <DiffViewer
                            onSendMessage={onSendMessage}
                            key={index}
                            suggestedChange={suggestedChange}
                            messageTypeClass={messageTypeClass}
                          />
                        ))}
                      </div>
                    )
                )}
              </>
            )}
            {message.suggestedContexts?.length > 0 && (
              <div className='mt-2'>
                <SuggestedContexts
                  suggestedContexts={message.suggestedContexts}
                  contexts={contexts}
                  updateContexts={updateContexts}
                />
              </div>
            )}
            {message?.additionalContent?.map((content, index) => {
              if (content.type === 'runbook' && content.content) {
                return (
                  <div key={index}>
                    <strong className='block mt-4 mb-2 text-sm'>
                      Generated Runbook:
                    </strong>
                    <RunbookViewer
                      runbook={{
                        name: content.buttonTitle || 'Generated Runbook',
                        content: content.content,
                        stored: false
                      }}
                      messageTypeClass={messageTypeClass}
                      onSendMessage={onSendMessage}
                    />
                  </div>
                );
              }
              return null;
            })}

            <div className='text-xs text-gray-400 mt-2 pt-2 border-t border-gray-500 border-opacity-30'>
              {finalTimestamp
                ? (() => {
                    const date = timestampToDate(finalTimestamp);
                    const dateStr = date.toLocaleString();
                    return dateStr === 'Invalid Date' ? '' : dateStr;
                  })()
                : ''}
            </div>

            {message?.steps?.length > 0 && (
              <div className='pt-4 pl-6 pb-2'>
                <Steps
                  direction='vertical'
                  size='default'
                  items={message.steps.map((step) => ({
                    title: <strong>{step.name}</strong>,
                    description: (
                      <>
                        <div
                          className='text-gray-500 mb-1'
                          dangerouslySetInnerHTML={{ __html: renderMarkdown(step.summary) }}
                        />
                        <span className='text-gray-500'>
                          {step.endTime
                            ? formatTime(JSON.parse(JSON.stringify(step.endTime)))
                            : formatTime(JSON.parse(JSON.stringify(step.startTime)))}
                        </span>
                      </>
                    ),
                    icon: step.status ==
                      AIConversationStepStatus.AI_CONVERSATION_STEP_STATUS_RUNNING && (
                      <FontAwesomeIcon color='#9ca3af' icon={faSpinner} spin={true} />
                    ),
                    status:
                      (step.status ==
                        AIConversationStepStatus.AI_CONVERSATION_STEP_STATUS_RUNNING &&
                        'process') ||
                      (step.status == AIConversationStepStatus.AI_CONVERSATION_STEP_STATUS_FAILED &&
                        'error') ||
                      'finish'
                  }))}
                  className='ai-message-steps'
                />
              </div>
            )}

            {!isUserMessage && (
              <div className='feedback-buttons'>
                <button
                  className={`feedback-button ${thumbsUpActive ? 'active' : ''}`}
                  onClick={() => handleFeedback(true)}
                >
                  <FontAwesomeIcon icon={faThumbsUp} />
                </button>
                <button
                  className={`feedback-button ${thumbsDownActive ? 'active' : ''}`}
                  onClick={() => handleFeedback(false)}
                >
                  <FontAwesomeIcon icon={faThumbsDown} />
                </button>
              </div>
            )}

            <div ref={ref} />
          </div>
        </div>
      </div>
    );
  }
);

const LoadingAnimation = () => (
  <div className='flex items-center space-x-2'>
    <div
      className='w-2 h-2 bg-blue-400 rounded-full animate-bounce'
      style={{ animationDelay: '0ms' }}
    />
    <div
      className='w-2 h-2 bg-blue-400 rounded-full animate-bounce'
      style={{ animationDelay: '150ms' }}
    />
    <div
      className='w-2 h-2 bg-blue-400 rounded-full animate-bounce'
      style={{ animationDelay: '300ms' }}
    />
  </div>
);

export const LoadingMessageBubble = () => (
  <div className='ml-0 mb-4 animate-fade-in'>
    <div className='p-3 rounded-lg message-bubble bot-message gradient-bg-blue'>
      <LoadingAnimation />
    </div>
  </div>
);
