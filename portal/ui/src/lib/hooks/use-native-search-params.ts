// Needed to handle argo cd extensions
import { useCallback, useEffect, useMemo, useState } from 'react';
import type { URLSearchParamsInit, useSearchParams } from 'react-router-dom';

const windowSearchParamsEventType = 'windowSearchParams';

export const useNativeSearchParams: typeof useSearchParams = (path) => {
  const [searchStr, setSearchStr] = useState('');

  useEffect(() => {
    const onEvent = () => setSearchStr(window.location.search);

    // Initialize with current search params immediately
    onEvent();

    window.addEventListener(windowSearchParamsEventType, onEvent);

    return () => window.removeEventListener(windowSearchParamsEventType, onEvent);
  }, []);

  const search = useMemo(() => new URLSearchParams(window.location.search), [searchStr]);

  return [
    search,
    useCallback(
      (nextSearch) => {
        let nextSearchParams: URLSearchParamsInit;
        if (typeof nextSearch === 'function') {
          nextSearchParams = nextSearch(search);
        } else {
          nextSearchParams = nextSearch;
        }
        window.history.replaceState(
          null,
          null,
          `${path || window.location.pathname}?${nextSearchParams.toString()}`
        );
        window.dispatchEvent(new PopStateEvent('popstate'));
        window.dispatchEvent(new CustomEvent(windowSearchParamsEventType));
      },
      [searchStr]
    )
  ];
};
