import type {
  GetSyncOperationsEventsForApplicationResponse,
  SyncOperationField
} from '@ui/lib/apiclient';

export const getSyncHistoryFilterSuggestions = async (
  appName: string,
  field: SyncOperationField,
  searchStr: string
) =>
  fetch('/ext-api/v1/argocd/extensions/sync-operations-events', {
    method: 'POST',
    body: JSON.stringify({
      field,
      fieldLike: searchStr,
      filter: {
        startTime: new Date(0).toISOString(),
        appName: [appName]
      }
    })
  })
    .then((res) => res.json() as GetSyncOperationsEventsForApplicationResponse)
    .then((res) => res.fieldResult);
