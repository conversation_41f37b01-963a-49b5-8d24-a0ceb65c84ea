package models

import (
	"reflect"
	"strings"
	"testing"

	"github.com/sashabaranov/go-openai/jsonschema"
	"github.com/stretchr/testify/assert"
)

func TestGetAIResponseParametersSchema(t *testing.T) {
	aiResponseSchema, err := GetAIResponseParametersSchema()
	assert.NoError(t, err)

	assert.Equal(t, jsonschema.Object, aiResponseSchema.Type)

	// Verify required fields are present
	expectedRequired := []string{
		"content",
		"thinkingProcess",
		"runbook",
		"suggestedContexts",
		"suggestedChanges",
		"needToolRun",
	}
	assert.ElementsMatch(t, expectedRequired, aiResponseSchema.Required)

	// Get the number of fields in AIResponse struct
	fields := reflect.TypeOf(AIResponse{})
	jsonSchemaFieldCount := 0
	for i := 0; i < fields.NumField(); i++ {
		field := fields.Field(i)
		if field.IsExported() || (field.Tag.Get("json") != "" && field.Tag.Get("json") != "-") {
			jsonSchemaFieldCount++
		}
	}
	assert.Equal(t, jsonSchemaFieldCount, len(aiResponseSchema.Properties))

	// Verify field types
	assert.Equal(t, jsonschema.String, aiResponseSchema.Properties["content"].Type)
	assert.Equal(t, jsonschema.String, aiResponseSchema.Properties["thinkingProcess"].Type)

	// Verify suggestedContexts array
	suggestedContexts := aiResponseSchema.Properties["suggestedContexts"]
	assert.Equal(t, jsonschema.Array, suggestedContexts.Type)
	assert.NotNil(t, suggestedContexts.Items)

	// Verify suggestedChanges array
	suggestedChanges := aiResponseSchema.Properties["suggestedChanges"]
	assert.Equal(t, jsonschema.Array, suggestedChanges.Type)
	assert.NotNil(t, suggestedChanges.Items)

	// Verify SuggestedChange item schema
	changeItemSchema := aiResponseSchema.Defs[strings.TrimPrefix(suggestedChanges.Items.Ref, "#/$defs/")]
	assert.Equal(t, jsonschema.Object, changeItemSchema.Type)
	expectedChangeFields := []string{"old", "patch", "new", "context", "applied"}
	assert.ElementsMatch(t, expectedChangeFields, changeItemSchema.Required)

	assert.Equal(t, jsonschema.String, changeItemSchema.Properties["old"].Type)
	assert.Equal(t, jsonschema.String, changeItemSchema.Properties["patch"].Type)
	assert.Equal(t, jsonschema.String, changeItemSchema.Properties["new"].Type)
	assert.Equal(t, jsonschema.Object, aiResponseSchema.Defs[strings.TrimPrefix(changeItemSchema.Properties["context"].Ref, "#/$defs/")].Type)
	assert.Equal(t, jsonschema.Boolean, changeItemSchema.Properties["applied"].Type)
}
